import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY') || '***********************************';

interface EnhancedChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context: {
    is_authenticated: boolean;
    profile_type?: string;
    profile_completion?: number;
    current_page?: string;
    user_id?: string;
    profile_data?: any;
    detailed_context?: {
      profile?: any;
      stats?: any;
      insights?: any;
      activity?: any[];
      connections?: any[];
      posts?: any[];
    };
  };
  include_detailed_context?: boolean;
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id: string;
}

interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon: string;
  url?: string;
  action?: string;
  color?: string;
}

const buildEnhancedSystemPrompt = (userContext: any): string => {
  let prompt = `You are ZbInnovation AI Assistant, an intelligent companion for Zimbabwe's premier digital innovation ecosystem.

PLATFORM CONTEXT:
ZbInnovation connects innovators, investors, mentors, professionals, academic institutions, industry experts, and organizations. The platform facilitates:
- Intelligent matchmaking between different user types
- Innovation project collaboration and funding opportunities
- Mentorship connections and knowledge sharing
- Community building and networking

YOUR ENHANCED CAPABILITIES:
- Provide deeply personalized guidance based on comprehensive user profile analysis
- Suggest specific platform actions with clickable buttons
- Offer contextual recommendations based on user insights and behavior
- Guide users through platform features and optimization strategies
- Provide data-driven insights about user's platform presence and growth

RESPONSE FORMAT:
- Always provide helpful, actionable advice tailored to the user's specific situation
- Include specific call-to-action buttons when relevant using [ACTION:type:label:icon:url/action:color] format
- Suggest next steps based on detailed user analysis and insights
- Be encouraging and supportive of innovation efforts
- Reference specific user data when providing recommendations
- Keep responses concise but comprehensive and personalized

USER CONTEXT:`;

  if (!userContext.is_authenticated) {
    prompt += `
- User is NOT authenticated
- Encourage sign-up/login to access full platform features
- Provide general platform information and benefits
- Include login/signup action buttons in responses
- Focus on platform value proposition and community benefits`;
  } else {
    prompt += `
- User is authenticated
- Profile Type: ${userContext.profile_type || 'Not specified'}
- Profile Completion: ${userContext.profile_completion || 0}%
- Current Page: ${userContext.current_page || 'Unknown'}`;

    // Add detailed context if available
    if (userContext.detailed_context) {
      const stats = userContext.detailed_context.stats;
      const insights = userContext.detailed_context.insights;
      const profile = userContext.detailed_context.profile;

      if (stats) {
        prompt += `
- DETAILED STATS:
  * Posts Created: ${stats.posts_count}
  * Connections: ${stats.connections_count}
  * Likes Received: ${stats.likes_received}
  * Comments Received: ${stats.comments_received}
  * Platform Activity: ${stats.activity_count} actions`;

        if (stats.join_date) {
          const joinDate = new Date(stats.join_date);
          const monthsAgo = Math.floor((Date.now() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
          prompt += `
  * Member Since: ${monthsAgo} months ago`;
        }
      }

      if (insights) {
        prompt += `
- USER INSIGHTS:
  * Profile Strength: ${insights.profile_strength}
  * Engagement Level: ${insights.engagement_level}
  * Network Size: ${insights.network_size}
  * Content Activity: ${insights.content_activity}`;

        if (insights.recommendations && insights.recommendations.length > 0) {
          prompt += `
  * AI Recommendations: ${insights.recommendations.join(', ')}`;
        }
      }

      if (profile?.first_name) {
        prompt += `
- User Name: ${profile.first_name}${profile.last_name ? ' ' + profile.last_name : ''}`;
      }
    }

    // Priority guidance based on profile completion and insights
    if (userContext.profile_completion < 50) {
      prompt += `
- PRIORITY: Encourage profile completion for better platform experience and visibility`;
    }

    if (userContext.detailed_context?.insights) {
      const insights = userContext.detailed_context.insights;

      if (insights.profile_strength === 'low') {
        prompt += `
- PRIORITY: Profile needs improvement - suggest specific profile enhancements`;
      }

      if (insights.content_activity === 'inactive') {
        prompt += `
- PRIORITY: User hasn't created content - encourage first post creation`;
      }

      if (insights.network_size === 'small') {
        prompt += `
- PRIORITY: Small network - suggest connection building strategies`;
      }
    }

    if (userContext.profile_type) {
      prompt += `
- Tailor responses for ${userContext.profile_type} specific needs and goals`;

      // Add profile type specific guidance
      switch (userContext.profile_type) {
        case 'innovator':
          prompt += `
- Focus on: Innovation sharing, investor connections, mentorship opportunities`;
          break;
        case 'investor':
          prompt += `
- Focus on: Innovation discovery, due diligence, portfolio building`;
          break;
        case 'mentor':
          prompt += `
- Focus on: Mentee connections, knowledge sharing, community leadership`;
          break;
      }
    }

    // Add page-specific context
    switch (userContext.current_page) {
      case 'dashboard':
        prompt += `
- User is on dashboard - focus on profile management, recent activity, and next steps`;
        break;
      case 'community':
        prompt += `
- User is on community page - focus on content creation, connections, and engagement`;
        break;
      case 'profile':
        prompt += `
- User is on profile page - focus on profile optimization and visibility`;
        break;
      case 'landing':
        prompt += `
- User is on landing page - focus on platform exploration and getting started`;
        break;
    }
  }

  prompt += `

AVAILABLE ACTIONS (use these in your responses based on user authentication status):

${userContext.is_authenticated ? `
AUTHENTICATED USER ACTIONS:
- [ACTION:navigation:Complete Profile:person:/dashboard/profile:primary]
- [ACTION:navigation:View Dashboard:dashboard:/dashboard:primary]
- [ACTION:action:Create Post:edit:create-post:accent]
- [ACTION:action:Find Mentors:school:find-mentors:secondary]
- [ACTION:action:Find Investors:attach_money:find-investors:positive]
- [ACTION:action:View Projects:work:view-projects:info]
- [ACTION:navigation:View Activity:timeline:/dashboard/activity:secondary]
- [ACTION:navigation:View Connections:people:/dashboard/connections:info]
- [ACTION:navigation:Explore Community:groups:/virtual-community?tab=feed:secondary]
- [ACTION:navigation:View Profiles:people:/virtual-community?tab=profiles:info]
- [ACTION:external:Learn More:info:https://zbinnovation.com:grey]
` : `
UNAUTHENTICATED USER ACTIONS:
- [ACTION:action:Sign Up:person_add:signup:primary]
- [ACTION:action:Sign In:login:signin:secondary]
- [ACTION:action:Join Community:groups:join-community:positive]
- [ACTION:navigation:Explore Community:groups:/virtual-community?tab=feed:secondary]
- [ACTION:navigation:View Profiles:people:/virtual-community?tab=profiles:info]
- [ACTION:external:Learn More:info:https://zbinnovation.com:grey]
`}

IMPORTANT: Only suggest actions appropriate for the user's authentication status. Do NOT suggest authenticated-only actions (like Create Post, Find Mentors, Complete Profile) to unauthenticated users.

TONE: Professional, encouraging, knowledgeable about African innovation, and specifically familiar with Zimbabwe's business environment.

Always include relevant action buttons to help users take immediate next steps.`;

  return prompt;
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  console.log('Enhanced AI Chat function called');

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    const requestBody = await req.json();
    console.log('Enhanced AI Chat request:', requestBody);

    const { message, conversation_history = [], user_context, include_detailed_context = true }: EnhancedChatRequest = requestBody;

    // Fetch detailed user context if requested and user is authenticated
    let enhancedUserContext = user_context;
    if (include_detailed_context && user_context.is_authenticated && user_context.user_id && !user_context.detailed_context) {
      console.log('Fetching detailed user context for:', user_context.user_id);

      try {
        const { data: contextData, error: contextError } = await supabase.functions.invoke('user-context', {
          body: {
            user_id: user_context.user_id,
            include_profile: true,
            include_stats: true,
            include_activity: false, // Don't include activity for chat context to keep response fast
            include_connections: false,
            include_posts: false
          }
        });

        if (!contextError && contextData) {
          enhancedUserContext = {
            ...user_context,
            detailed_context: {
              profile: contextData.profile,
              stats: contextData.stats,
              insights: contextData.insights
            }
          };
          console.log('Detailed user context fetched successfully');
        } else {
          console.warn('Failed to fetch detailed user context:', contextError);
        }
      } catch (error) {
        console.warn('Error fetching detailed user context:', error);
      }
    }

    if (!message || typeof message !== 'string') {
      console.error('Invalid message:', message);
      return new Response(
        JSON.stringify({ error: 'Message is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Build enhanced system prompt with detailed context
    const systemPrompt = buildEnhancedSystemPrompt(enhancedUserContext);
    console.log('Built enhanced system prompt with detailed context:', {
      is_authenticated: enhancedUserContext.is_authenticated,
      has_detailed_context: !!enhancedUserContext.detailed_context,
      profile_completion: enhancedUserContext.profile_completion,
      profile_type: enhancedUserContext.profile_type
    });

    // Build conversation messages
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversation_history,
      { role: 'user', content: message }
    ];

    console.log('Calling DeepSeek API with enhanced context');

    // Check if streaming is requested
    const isStreaming = request.headers.get('accept') === 'text/event-stream';

    // Call DeepSeek API
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: messages,
        max_tokens: 1500,
        temperature: 0.7,
        stream: isStreaming
      })
    });

    console.log('DeepSeek API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API error:', response.status, errorText);
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    // Handle streaming response
    if (isStreaming) {
      return handleStreamingResponse(response, enhancedUserContext);
    }

    const data = await response.json();
    console.log('DeepSeek API response received');

    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      console.error('No AI response in data:', data);
      throw new Error('No response from AI model');
    }

    // Parse action buttons from response
    const actions = parseActionButtons(aiResponse);
    const cleanResponse = aiResponse.replace(/\[ACTION:.*?\]/g, '').trim();

    // Generate contextual suggestions with enhanced context
    const suggestions = generateContextualSuggestions(enhancedUserContext);

    const result: AIResponse = {
      response: cleanResponse,
      actions,
      suggestions,
      conversation_id: crypto.randomUUID()
    };

    console.log('Enhanced AI response prepared:', {
      responseLength: cleanResponse.length,
      actionsCount: actions.length,
      suggestionsCount: suggestions.length
    });

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('Enhanced AI Chat error:', error);

    // Provide intelligent fallback based on user context
    const fallbackResponse = generateFallbackResponse(error.message);

    return new Response(
      JSON.stringify({
        response: fallbackResponse.response,
        actions: fallbackResponse.actions,
        suggestions: fallbackResponse.suggestions,
        error: 'AI service temporarily unavailable',
        conversation_id: crypto.randomUUID()
      }),
      {
        status: 200, // Return 200 with fallback instead of error
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

function parseActionButtons(response: string): ActionButton[] {
  const actionRegex = /\[ACTION:(.*?):(.*?):(.*?):(.*?):(.*?)\]/g;
  const actions: ActionButton[] = [];
  let match;

  while ((match = actionRegex.exec(response)) !== null) {
    const [, type, label, icon, urlOrAction, color] = match;
    actions.push({
      type: type as 'navigation' | 'action' | 'external',
      label,
      icon,
      url: type === 'external' || type === 'navigation' ? urlOrAction : undefined,
      action: type === 'action' ? urlOrAction : undefined,
      color: color || 'primary'
    });
  }

  return actions;
}

function generateContextualSuggestions(userContext: any): string[] {
  const suggestions: string[] = [];

  if (!userContext.is_authenticated) {
    suggestions.push(
      "How do I sign up for ZbInnovation?",
      "What are the benefits of joining the platform?",
      "Tell me about the innovation community",
      "How does the platform help innovators?"
    );
  } else {
    // Enhanced authenticated user suggestions based on detailed context
    const insights = userContext.detailed_context?.insights;
    const stats = userContext.detailed_context?.stats;

    // Priority suggestions based on insights
    if (insights?.profile_strength === 'low') {
      suggestions.push("How can I improve my profile to attract more connections?");
    }

    if (insights?.content_activity === 'inactive' && stats?.posts_count === 0) {
      suggestions.push("What should I include in my first post?");
    }

    if (insights?.network_size === 'small' && stats?.connections_count < 5) {
      suggestions.push("How do I find and connect with the right people?");
    }

    if (insights?.engagement_level === 'low') {
      suggestions.push("How can I increase my engagement on the platform?");
    }

    // Page-specific suggestions with context awareness
    switch (userContext.current_page) {
      case 'dashboard':
        if (stats?.posts_count === 0) {
          suggestions.push("How do I create my first post?");
        } else {
          suggestions.push("Show me my recent activity");
        }

        if (stats?.connections_count < 10) {
          suggestions.push("How do I connect with other innovators?");
        }

        suggestions.push("What should I do next to grow my presence?");
        break;

      case 'community':
        suggestions.push(
          "Help me find relevant groups to join",
          "What events are coming up?",
          "How do I share my innovation story?",
          "How can I connect with mentors?"
        );
        break;

      case 'profile':
        if (userContext.profile_completion < 80) {
          suggestions.push("How can I complete my profile effectively?");
        }
        suggestions.push(
          "How can I improve my profile visibility?",
          "What should I include in my bio?",
          "How do I showcase my innovations?",
          "How do I attract the right connections?"
        );
        break;

      default:
        suggestions.push(
          "What can I do on this platform?",
          "How do I get started?",
          "Show me platform features",
          "Help me navigate the platform"
        );
    }

    // Profile type and stats specific suggestions
    if (userContext.profile_type && stats) {
      switch (userContext.profile_type) {
        case 'innovator':
          if (stats.posts_count < 3) {
            suggestions.push("How do I effectively showcase my innovations?");
          }
          if (stats.connections_count < 5) {
            suggestions.push("How do I find investors for my innovation?");
          }
          break;

        case 'investor':
          if (stats.connections_count < 10) {
            suggestions.push("How do I discover promising innovations to invest in?");
          }
          if (stats.activity_count < 5) {
            suggestions.push("How do I evaluate innovation opportunities?");
          }
          break;

        case 'mentor':
          if (stats.activity_count < 10) {
            suggestions.push("How do I offer mentorship to innovators?");
          }
          if (stats.connections_count < 15) {
            suggestions.push("How do I find innovators who need mentoring?");
          }
          break;
      }
    }

    // Add insights-based recommendations as suggestions
    if (insights?.recommendations && insights.recommendations.length > 0) {
      const recommendation = insights.recommendations[0];
      suggestions.push(`Help me: ${recommendation.toLowerCase()}`);
    }
  }

  // Remove duplicates and limit to 4 suggestions
  const uniqueSuggestions = [...new Set(suggestions)];
  return uniqueSuggestions.slice(0, 4);
}

function generateFallbackResponse(errorMessage: string): AIResponse {
  return {
    response: `I apologize, but I'm experiencing technical difficulties at the moment. However, I'm here to help you navigate the ZbInnovation platform and connect with Zimbabwe's innovation community. 

While I work on resolving this issue, you can explore the platform features using the buttons below, or try asking your question again in a moment.`,
    actions: [
      {
        type: 'navigation',
        label: 'Explore Community',
        icon: 'groups',
        url: '/virtual-community?tab=feed',
        color: 'primary'
      },
      {
        type: 'navigation',
        label: 'View Dashboard',
        icon: 'dashboard',
        url: '/dashboard',
        color: 'secondary'
      }
    ],
    suggestions: [
      "Try asking your question again",
      "How do I get started on the platform?",
      "What features are available?",
      "Help me navigate ZbInnovation"
    ],
    conversation_id: crypto.randomUUID()
  };
}

async function handleStreamingResponse(response: Response, userContext: any): Promise<Response> {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();

  let fullResponse = '';

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('No response body');
        }

        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Send final message with actions and suggestions
            const actions = parseActionButtons(fullResponse);
            const cleanResponse = fullResponse.replace(/\[ACTION:.*?\]/g, '').trim();
            const suggestions = generateContextualSuggestions(userContext);

            const finalData = {
              type: 'complete',
              actions,
              suggestions,
              conversation_id: crypto.randomUUID()
            };

            controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalData)}\n\n`));
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
            break;
          }

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                continue;
              }

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;

                if (content) {
                  fullResponse += content;

                  // Send streaming chunk
                  const streamData = {
                    type: 'chunk',
                    content: content
                  };

                  controller.enqueue(encoder.encode(`data: ${JSON.stringify(streamData)}\n\n`));
                }
              } catch (e) {
                console.warn('Failed to parse streaming chunk:', e);
              }
            }
          }
        }
      } catch (error) {
        console.error('Streaming error:', error);
        controller.error(error);
      }
    }
  });

  return new Response(stream, {
    headers: {
      ...corsHeaders,
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  });
}
