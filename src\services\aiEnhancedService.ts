/**
 * Enhanced AI Service
 * 
 * This service provides enhanced AI chat functionality with authentication integration,
 * user context awareness, call-to-action buttons, and contextual suggestions.
 */

import { supabase } from '../lib/supabase';
import { useAuthStore } from '../stores/auth';
import { useRoute, useRouter } from 'vue-router';
import { useProfileStore } from '../stores/profileStore';

export interface EnhancedChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
  actions?: ActionButton[];
  suggestions?: string[];
}

export interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon: string;
  url?: string;
  action?: string;
  color?: string;
}

export interface EnhancedChatRequest {
  message: string;
  conversation_history?: EnhancedChatMessage[];
  user_context?: UserContext;
}

export interface UserContext {
  is_authenticated: boolean;
  profile_type?: string;
  profile_completion?: number;
  current_page?: string;
  user_id?: string;
  profile_data?: any;
  detailed_context?: UserDetailedContext;
}

export interface UserDetailedContext {
  profile?: any;
  stats?: {
    profile_completion: number;
    posts_count: number;
    connections_count: number;
    activity_count: number;
    likes_received: number;
    comments_received: number;
    last_active: string;
    join_date: string;
  };
  insights?: {
    profile_strength: 'low' | 'medium' | 'high';
    engagement_level: 'low' | 'medium' | 'high';
    network_size: 'small' | 'medium' | 'large';
    content_activity: 'inactive' | 'moderate' | 'active';
    recommendations: string[];
  };
  activity?: any[];
  connections?: any[];
  posts?: any[];
}

export interface EnhancedChatResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id?: string;
  error?: string;
}

/**
 * Custom error class for AI service failures
 */
export class AIServiceError extends Error {
  constructor(
    message: string,
    public type: 'connection' | 'authentication' | 'rate_limit' | 'service_unavailable' | 'unknown',
    public details?: string
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

/**
 * Error response interface for structured error information
 */
export interface ErrorResponse {
  message: string;
  type: 'connection' | 'authentication' | 'rate_limit' | 'service_unavailable' | 'unknown';
  details?: string;
  userGuidance: string;
}

/**
 * Create a structured error response based on the error type
 */
function createErrorResponse(error: any, userContext?: UserContext): ErrorResponse {
  console.error('Creating error response for:', error);

  // Check for specific error types
  if (error?.message?.includes('fetch') || error?.message?.includes('network') || error?.code === 'NETWORK_ERROR') {
    return {
      message: 'AI service connection failed',
      type: 'connection',
      details: 'Unable to connect to the AI service. This may be due to network connectivity issues.',
      userGuidance: 'Please check your internet connection and try again. If the problem persists, contact support.'
    };
  }

  if (error?.message?.includes('auth') || error?.message?.includes('unauthorized') || error?.status === 401) {
    return {
      message: 'AI service authentication error',
      type: 'authentication',
      details: 'The AI service authentication has failed or expired.',
      userGuidance: 'This is a technical issue on our end. Please try again later or contact support if this persists.'
    };
  }

  if (error?.message?.includes('rate') || error?.message?.includes('limit') || error?.status === 429) {
    return {
      message: 'AI service rate limit exceeded',
      type: 'rate_limit',
      details: 'Too many requests have been made to the AI service.',
      userGuidance: 'Please wait a moment before trying again. The service will be available shortly.'
    };
  }

  if (error?.status >= 500 || error?.message?.includes('server') || error?.message?.includes('unavailable')) {
    return {
      message: 'AI service temporarily unavailable',
      type: 'service_unavailable',
      details: 'The AI service is experiencing technical difficulties.',
      userGuidance: 'Our AI assistant is temporarily unavailable. Please try again in a few minutes.'
    };
  }

  // Default unknown error
  return {
    message: 'AI service error',
    type: 'unknown',
    details: error?.message || 'An unexpected error occurred while communicating with the AI service.',
    userGuidance: 'Something went wrong with the AI service. Please try again or contact support if this continues.'
  };
}

/**
 * Send an enhanced chat message with full context awareness
 */
export async function sendEnhancedChatMessage(request: EnhancedChatRequest): Promise<EnhancedChatResponse> {
  try {
    console.log('Sending enhanced chat message:', request);

    const { data, error } = await supabase.functions.invoke('ai-enhanced-chat', {
      body: request
    });

    if (error) {
      console.error('Enhanced AI Chat Service Error:', error);

      // Create a detailed error response based on the error type
      const errorResponse = createErrorResponse(error, request.user_context);
      throw new AIServiceError(errorResponse.message, errorResponse.type, errorResponse.details);
    }

    console.log('Enhanced AI response received:', data);
    return data;
  } catch (error: any) {
    console.error('Enhanced AI Chat Service Error:', error);

    // Re-throw the error to be handled by the calling component
    if (error instanceof AIServiceError) {
      throw error;
    }

    // For unknown errors, create a generic error response
    const errorResponse = createErrorResponse(error, request.user_context);
    throw new AIServiceError(errorResponse.message, errorResponse.type, errorResponse.details);
  }
}

/**
 * Send enhanced chat message with streaming support
 */
export async function sendEnhancedChatMessageStream(
  request: EnhancedChatRequest,
  onChunk: (content: string) => void,
  onComplete: (actions: ActionButton[], suggestions: string[]) => void
): Promise<void> {
  try {
    console.log('Sending streaming enhanced chat message');

    const { data: { session } } = await supabase.auth.getSession();
    const token = session?.access_token;

    const response = await fetch(`${supabase.supabaseUrl}/functions/v1/ai-enhanced-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            return;
          }

          try {
            const parsed = JSON.parse(data);

            if (parsed.type === 'chunk' && parsed.content) {
              onChunk(parsed.content);
            } else if (parsed.type === 'complete') {
              onComplete(parsed.actions || [], parsed.suggestions || []);
            }
          } catch (e) {
            console.warn('Failed to parse streaming data:', e);
          }
        }
      }
    }
  } catch (error: any) {
    console.error('Streaming AI service error:', error);
    throw new AIServiceError(error.message || 'Failed to stream AI response');
  }
}

/**
 * Build comprehensive user context for AI chat
 */
export function buildUserContext(currentPath?: string): UserContext {
  const authStore = useAuthStore();
  const profileStore = useProfileStore();

  const context: UserContext = {
    is_authenticated: authStore.isAuthenticated,
    current_page: getCurrentPageContext(currentPath || window.location.pathname),
  };

  // Add authenticated user context
  if (authStore.isAuthenticated && authStore.user) {
    context.user_id = authStore.user.id;
    context.profile_type = authStore.user.user_metadata?.profile_type;

    // Get profile completion from profile store if available
    try {
      const currentProfile = profileStore.currentProfile;
      if (currentProfile) {
        context.profile_completion = currentProfile.profile_completion || 0;
        context.profile_data = {
          first_name: currentProfile.first_name,
          last_name: currentProfile.last_name,
          profile_state: currentProfile.profile_state,
          profile_visibility: currentProfile.profile_visibility
        };
      }
    } catch (error) {
      console.warn('Could not fetch profile data for AI context:', error);
    }
  }

  console.log('Built user context for AI:', context);
  return context;
}

/**
 * Build detailed user context with comprehensive profile data
 */
export async function buildDetailedUserContext(includeActivity = false, includeConnections = false, currentPath?: string): Promise<UserContext> {
  const basicContext = buildUserContext(currentPath);

  if (!basicContext.is_authenticated || !basicContext.user_id) {
    return basicContext;
  }

  try {
    console.log('Fetching detailed user context for:', basicContext.user_id);

    const { data, error } = await supabase.functions.invoke('user-context', {
      body: {
        user_id: basicContext.user_id,
        include_profile: true,
        include_stats: true,
        include_activity: includeActivity,
        include_connections: includeConnections,
        include_posts: false // We'll add this later if needed
      }
    });

    if (error) {
      console.error('Error fetching detailed user context:', error);
      return basicContext;
    }

    if (data) {
      basicContext.detailed_context = {
        profile: data.profile,
        stats: data.stats,
        insights: data.insights,
        activity: data.activity,
        connections: data.connections,
        posts: data.posts
      };

      // Update basic context with detailed data
      if (data.stats) {
        basicContext.profile_completion = data.stats.profile_completion;
      }

      if (data.profile) {
        basicContext.profile_type = data.profile.profile_type || basicContext.profile_type;
        basicContext.profile_data = {
          ...basicContext.profile_data,
          ...data.profile
        };
      }

      console.log('Detailed user context fetched successfully:', {
        has_profile: !!data.profile,
        has_stats: !!data.stats,
        has_insights: !!data.insights,
        profile_completion: data.stats?.profile_completion,
        recommendations_count: data.insights?.recommendations?.length || 0
      });
    }

  } catch (error) {
    console.error('Failed to fetch detailed user context:', error);
  }

  return basicContext;
}

/**
 * Determine current page context for AI awareness
 */
function getCurrentPageContext(path: string): string {
  if (path === '/') return 'landing';
  if (path.includes('/dashboard')) {
    if (path.includes('/profile')) return 'profile';
    if (path.includes('/content')) return 'content';
    if (path.includes('/connections')) return 'connections';
    if (path.includes('/activity')) return 'activity';
    return 'dashboard';
  }
  if (path.includes('/virtual-community')) return 'community';
  if (path.includes('/auth')) return 'auth';
  return 'other';
}

/**
 * Execute an action button click
 */
export async function executeAction(action: ActionButton): Promise<void> {
  console.log('Executing action:', action);

  try {
    switch (action.type) {
      case 'navigation':
        await handleNavigationAction(action);
        break;
      case 'action':
        await handlePlatformAction(action.action!);
        break;
      case 'external':
        handleExternalAction(action);
        break;
      default:
        console.warn('Unknown action type:', action.type);
    }
  } catch (error) {
    console.error('Error executing action:', error);
    throw error;
  }
}

/**
 * Handle navigation actions
 */
async function handleNavigationAction(action: ActionButton): Promise<void> {
  if (!action.url) {
    console.error('Navigation action missing URL:', action);
    return;
  }

  try {
    // Use window location for navigation to avoid router context issues
    window.location.href = action.url;
    console.log('Navigation completed to:', action.url);
  } catch (error) {
    console.error('Navigation error:', error);
  }
}

/**
 * Handle platform-specific actions
 */
async function handlePlatformAction(actionType: string): Promise<void> {
  console.log('Handling platform action:', actionType);

  switch (actionType) {
    case 'create-post':
      await triggerCreatePost();
      break;
    case 'complete-profile':
      await navigateToProfileCompletion();
      break;
    case 'signin':
      await triggerSignIn();
      break;
    case 'signup':
      await triggerSignUp();
      break;
    case 'view-activity':
      await navigateToActivity();
      break;
    case 'view-connections':
      await navigateToConnections();
      break;
    case 'find-mentors':
      await navigateToFindMentors();
      break;
    case 'find-investors':
      await navigateToFindInvestors();
      break;
    case 'view-projects':
      await navigateToProjects();
      break;
    case 'join-community':
      await navigateToJoinCommunity();
      break;
    default:
      console.warn('Unknown platform action:', actionType);
      throw new Error(`Unknown platform action: ${actionType}`);
  }
}

/**
 * Handle external link actions
 */
function handleExternalAction(action: ActionButton): void {
  if (action.url) {
    window.open(action.url, '_blank', 'noopener,noreferrer');
    console.log('External link opened:', action.url);
  }
}

/**
 * Platform action implementations
 */
async function triggerCreatePost(): Promise<void> {
  // Navigate to community page with create intent
  window.location.href = '/virtual-community?tab=feed&action=create';
}

async function navigateToProfileCompletion(): Promise<void> {
  window.location.href = '/dashboard/profile?action=complete';
}

async function triggerSignIn(): Promise<void> {
  window.location.href = '/auth/signin';
}

async function triggerSignUp(): Promise<void> {
  window.location.href = '/auth/signup';
}

async function navigateToActivity(): Promise<void> {
  window.location.href = '/dashboard/activity';
}

async function navigateToConnections(): Promise<void> {
  window.location.href = '/dashboard/connections';
}

async function navigateToFindMentors(): Promise<void> {
  window.location.href = '/virtual-community?tab=profiles&filter=mentor';
}

async function navigateToFindInvestors(): Promise<void> {
  window.location.href = '/virtual-community?tab=profiles&filter=investor';
}

async function navigateToProjects(): Promise<void> {
  window.location.href = '/virtual-community?tab=projects';
}

async function navigateToJoinCommunity(): Promise<void> {
  window.location.href = '/virtual-community?tab=feed';
}



/**
 * Validate action button before execution
 */
export function validateAction(action: ActionButton): boolean {
  // Check if action is a valid object
  if (!action || typeof action !== 'object') {
    console.error('Invalid action button: not an object', action);
    return false;
  }

  // Check required fields
  if (!action.type) {
    console.error('Invalid action button: missing type', action);
    return false;
  }

  if (!action.label) {
    console.error('Invalid action button: missing label', action);
    return false;
  }

  if (!action.icon) {
    console.error('Invalid action button: missing icon', action);
    return false;
  }

  // Validate type-specific requirements
  switch (action.type) {
    case 'navigation':
      if (!action.url) {
        console.error('Invalid navigation action: missing url', action);
        return false;
      }
      return true;
    case 'action':
      if (!action.action) {
        console.error('Invalid platform action: missing action', action);
        return false;
      }
      return true;
    case 'external':
      if (!action.url) {
        console.error('Invalid external action: missing url', action);
        return false;
      }
      return true;
    default:
      console.error('Invalid action button: unknown type', action.type, action);
      return false;
  }
}

/**
 * Track action execution for analytics
 */
export function trackActionExecution(action: ActionButton, success: boolean): void {
  console.log('Action execution tracked:', {
    type: action.type,
    label: action.label,
    action: action.action || action.url,
    success,
    timestamp: new Date().toISOString()
  });

  // TODO: Implement actual analytics tracking
  // This could integrate with existing activity tracking service
}

/**
 * Get suggested actions based on user context
 */
export function getSuggestedActions(userContext: UserContext): ActionButton[] {
  const actions: ActionButton[] = [];

  if (!userContext.is_authenticated) {
    actions.push(
      {
        type: 'action',
        label: 'Sign Up',
        icon: 'person_add',
        action: 'signup',
        color: 'primary'
      },
      {
        type: 'navigation',
        label: 'Explore Community',
        icon: 'groups',
        url: '/virtual-community?tab=feed',
        color: 'secondary'
      }
    );
  } else {
    // Enhanced authenticated user actions based on detailed context
    const insights = userContext.detailed_context?.insights;
    const stats = userContext.detailed_context?.stats;

    // Priority actions based on insights
    if (insights?.profile_strength === 'low') {
      actions.push({
        type: 'action',
        label: 'Complete Profile',
        icon: 'person',
        action: 'complete-profile',
        color: 'primary'
      });
    }

    if (insights?.content_activity === 'inactive') {
      actions.push({
        type: 'action',
        label: 'Create First Post',
        icon: 'edit',
        action: 'create-post',
        color: 'accent'
      });
    }

    if (insights?.network_size === 'small') {
      actions.push({
        type: 'navigation',
        label: 'Find Connections',
        icon: 'people',
        url: '/virtual-community?tab=profiles',
        color: 'info'
      });
    }

    // Page-specific actions
    switch (userContext.current_page) {
      case 'dashboard':
        if (!actions.some(a => a.action === 'create-post')) {
          actions.push({
            type: 'action',
            label: 'Create Post',
            icon: 'edit',
            action: 'create-post',
            color: 'accent'
          });
        }
        break;
      case 'community':
        if (!actions.some(a => a.url?.includes('profiles'))) {
          actions.push({
            type: 'navigation',
            label: 'View Profiles',
            icon: 'people',
            url: '/virtual-community?tab=profiles',
            color: 'info'
          });
        }
        break;
    }

    // Profile type specific actions
    if (userContext.profile_type && stats) {
      switch (userContext.profile_type) {
        case 'innovator':
          if (stats.posts_count < 3) {
            actions.push({
              type: 'action',
              label: 'Share Innovation',
              icon: 'lightbulb',
              action: 'create-post',
              color: 'orange'
            });
          }
          break;
        case 'investor':
          if (stats.connections_count < 10) {
            actions.push({
              type: 'navigation',
              label: 'Discover Innovators',
              icon: 'search',
              url: '/virtual-community?tab=profiles&filter=innovator',
              color: 'green'
            });
          }
          break;
        case 'mentor':
          if (stats.activity_count < 5) {
            actions.push({
              type: 'navigation',
              label: 'Find Mentees',
              icon: 'school',
              url: '/virtual-community?tab=profiles&filter=innovator',
              color: 'purple'
            });
          }
          break;
      }
    }
  }

  return actions.slice(0, 3); // Limit to 3 suggested actions
}

/**
 * Get profile-based recommendations for AI responses
 */
export function getProfileRecommendations(userContext: UserContext): string[] {
  const recommendations: string[] = [];

  if (!userContext.is_authenticated || !userContext.detailed_context) {
    return recommendations;
  }

  const insights = userContext.detailed_context.insights;
  const stats = userContext.detailed_context.stats;

  if (insights?.recommendations) {
    recommendations.push(...insights.recommendations);
  }

  // Add contextual recommendations based on current page
  switch (userContext.current_page) {
    case 'dashboard':
      if (stats && stats.posts_count === 0) {
        recommendations.push('Create your first post to introduce yourself to the community');
      }
      if (stats && stats.connections_count < 5) {
        recommendations.push('Connect with at least 5 people to build your network');
      }
      break;
    case 'community':
      if (stats && stats.activity_count < 10) {
        recommendations.push('Engage with posts by liking and commenting to build relationships');
      }
      break;
    case 'profile':
      if (userContext.profile_completion && userContext.profile_completion < 80) {
        recommendations.push('Add more details to your profile to attract the right connections');
      }
      break;
  }

  return recommendations.slice(0, 5); // Limit to 5 recommendations
}

/**
 * Generate personalized AI context summary
 */
export function generatePersonalizedContext(userContext: UserContext): string {
  if (!userContext.is_authenticated) {
    return 'User is not authenticated. Focus on platform benefits and encourage signup.';
  }

  const insights = userContext.detailed_context?.insights;
  const stats = userContext.detailed_context?.stats;
  const profile = userContext.detailed_context?.profile;

  let context = `User is authenticated as ${userContext.profile_type || 'user'}.`;

  if (profile?.first_name) {
    context += ` Name: ${profile.first_name}`;
    if (profile.last_name) context += ` ${profile.last_name}`;
    context += '.';
  }

  if (stats) {
    context += ` Profile completion: ${stats.profile_completion}%.`;
    context += ` Posts: ${stats.posts_count}, Connections: ${stats.connections_count}.`;

    if (stats.join_date) {
      const joinDate = new Date(stats.join_date);
      const monthsAgo = Math.floor((Date.now() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
      context += ` Member for ${monthsAgo} months.`;
    }
  }

  if (insights) {
    context += ` Profile strength: ${insights.profile_strength}.`;
    context += ` Engagement level: ${insights.engagement_level}.`;
    context += ` Network size: ${insights.network_size}.`;
    context += ` Content activity: ${insights.content_activity}.`;
  }

  context += ` Current page: ${userContext.current_page}.`;

  return context;
}
